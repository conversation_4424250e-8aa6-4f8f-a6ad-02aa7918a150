class Node:
    def __init__(self, data):
        self.data = data
        self.next = None

class LinkedListQueue:
    def __init__(self):
        self.head = None
        self.tail = None

    def enqueue(self, x):
        # Add an element to the end of the queue and return the new node.
        new_node = Node(x)
        if self.tail:
            self.tail.next = new_node
        else:
            self.head = new_node

        self.tail = new_node
        return new_node

    def dequeue(self):
        # Remove and return the front element of the queue.
        if not self.head:
            raise IndexError("Queue underflow: cannot dequeue from empty queue")
        value = self.head.data
        self.head = self.head.next

        if not self.head:
            self.tail = None
        return value

    def to_list(self):
        # Helper to visualize queue contents (head -> ... -> tail)
        output = []
        current = self.head
        while current:
            output.append(current.data)
            current = current.next
        return output


# Demo using the linked-list-backed queue
llq = LinkedListQueue()
head = llq.enqueue(10)
print("Enqueued head data:", head.data)
llq.enqueue(20)
llq.enqueue(30)
print("Queue (linked list):", llq.to_list())
print("Dequeued:", llq.dequeue())
print("Queue after dequeue:", llq.to_list())
print("Dequeued:", llq.dequeue())
print("Queue after dequeue:", llq.to_list())


# The following list-based queue demo was present earlier and is left commented out.
"""
queue = []
queue.append(10)
queue.append(20)
queue.append(30)
print("Queue:", queue)  # [10, 20, 30]

# Dequeue
first = queue.pop(0)  # Removes first element
print("Dequeued:", first)
print("Queue after dequeue:", queue)
queue.append(10)
"""