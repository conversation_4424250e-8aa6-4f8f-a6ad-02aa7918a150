class QueueTwoStacksEnqueueCostly:
    def __init__(self):
        self.input_stack = []
        self.output_stack = []

    def enqueue(self, x):
        # Step 1: Move everything to input_stack
        while self.output_stack:
            self.input_stack.append(self.output_stack.pop())

        # Step 2: Add new item
        self.input_stack.append(x)
        print(f"📥Append {x} into Input Stack: ",self.input_stack)
        # Step 3: Move everything back to output_stack
        while self.input_stack:
            self.output_stack.append(self.input_stack.pop())

    def dequeue(self):
        if not self.output_stack:
            raise IndexError("Queue underflow: cannot dequeue from empty queue")
        return self.output_stack.pop()
    
    # Helper to visualize queues
    def debug(self):
        print("📥 Input Stack:", self.input_stack)
        print(f"📤 Output Stack: {self.output_stack}\n")

q = QueueTwoStacksEnqueueCostly()
q.enqueue(23)
q.enqueue(13)
q.enqueue(11)
q.debug()

print("Dequeued:", q.dequeue())  
print("Dequeued:", q.dequeue())  
print("Dequeued:", q.dequeue()) 
q.debug()
