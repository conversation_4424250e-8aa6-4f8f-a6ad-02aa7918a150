class QueueTwoStacksDequeueCostly:
    def __init__(self):
        self.input_stack = []
        self.output_stack = []

    def enqueue(self, x):
        self.input_stack.append(x)
        return self.input_stack  # Optional: for visibility

    def dequeue(self):
        if len(self.output_stack) == 0:
            # Transfer all items from input_stack to output_stack
            while self.input_stack:
                self.output_stack.append(self.input_stack.pop())

        if len(self.output_stack) == 0:
            raise IndexError("Queue underflow: cannot dequeue from empty queue")

        return self.output_stack.pop()

    def debug(self):
        print("📥 Input Stack:", self.input_stack)
        print("📤 Output Stack:", self.output_stack)

twoStackQueue = QueueTwoStacksDequeueCostly()
twoStackQueue.enqueue(10)
twoStackQueue.enqueue(20)
twoStackQueue.enqueue(30)
twoStackQueue.debug()

print("Dequeued:", twoStackQueue.dequeue())
twoStackQueue.debug()

print("Dequeued:", twoStackQueue.dequeue())
twoStackQueue.debug()

twoStackQueue.enqueue(2)
twoStackQueue.debug()

print("Dequeued:", twoStackQueue.dequeue())
twoStackQueue.debug()

print("Dequeued:", twoStackQueue.dequeue())
twoStackQueue.debug()