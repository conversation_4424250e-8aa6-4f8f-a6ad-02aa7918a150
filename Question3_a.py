class DoubleHashTable:
    def __init__(self):
        self.m = 11  # table size
        self.p = 7   # secondary prime
        self.keys = [None] * self.m
        self.values = [None] * self.m
        self.tombstone = "DELETED"
    
    def S(self, key):
        """S(k) = sum of ASCII codes of characters in k"""
        return sum(ord(char) for char in key)
    
    def h1(self, key):
        """h1(k) = S(k) mod m"""
        return self.S(key) % self.m
    
    def h2(self, key):
        """h2(k) = p - (S(k) mod p) (use 1 if this becomes 0)"""
        result = self.p - (self.S(key) % self.p)
        return result if result != 0 else 1
    
    def probe_sequence(self, key, i):
        """index_i = (h1(k) + i * h2(k)) mod m"""
        return (self.h1(key) + i * self.h2(key)) % self.m
    
    def put(self, key, value):
        """Insert key-value pair using double hashing"""
        i = 0
        probe_steps = []
        
        while i < self.m:
            index = self.probe_sequence(key, i)
            probe_steps.append(index)
            
            if self.keys[index] is None or self.keys[index] == self.tombstone:
                self.keys[index] = key
                self.values[index] = value
                return probe_steps
            
            i += 1
        
        raise Exception("Hash table is full")
    
    def get(self, key):
        """Get value by key"""
        i = 0
        while i < self.m:
            index = self.probe_sequence(key, i)
            
            if self.keys[index] is None:
                return None
            elif self.keys[index] == key:
                return self.values[index]
            
            i += 1
        return None
    
    def delete(self, key):
        """Delete key using tombstone"""
        i = 0
        while i < self.m:
            index = self.probe_sequence(key, i)
            
            if self.keys[index] is None:
                return False
            elif self.keys[index] == key:
                self.keys[index] = self.tombstone
                self.values[index] = None
                return True
            
            i += 1
        return False
