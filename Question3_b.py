class BSTNode:
    def __init__(self, key, value):
        self.key = key
        self.value = value
        self.left = None
        self.right = None

class BST:
    def __init__(self):
        self.root = None
    
    def insert(self, key, value):
        """Insert key-value pair into BST - no duplicate keys allowed"""
        result = self._insert(self.root, key, value)
        if result is None:
            raise ValueError(f"Key '{key}' already exists in BST. Duplicate keys not allowed.")
        self.root = result

    def _insert(self, node, key, value):
        """Helper insert method - returns None if key already exists"""
        if node is None:
            return BSTNode(key, value)

        # String comparison (lexicographical)
        if key < node.key:
            left_result = self._insert(node.left, key, value)
            if left_result is None:
                return None  # Duplicate found in left subtree
            node.left = left_result
        elif key > node.key:
            right_result = self._insert(node.right, key, value)
            if right_result is None:
                return None  # Duplicate found in right subtree
            node.right = right_result
        else:
            # Key already exists - no duplicates allowed
            return None

        return node
    
    def search(self, key):
        """Search for key in BST"""
        return self._search(self.root, key)
    
    def _search(self, node, key):
        """Helper search method"""
        if node is None:
            return None
        
        if key == node.key:
            return node.value
        elif key < node.key:
            return self._search(node.left, key)
        else:
            return self._search(node.right, key)
    
    def inorder(self):
        """Return list of (key, value) pairs in sorted order"""
        result = []
        self._inorder(self.root, result)
        return result
    
    def _inorder(self, node, result):
        """Helper inorder traversal"""
        if node:
            self._inorder(node.left, result)
            result.append((node.key, node.value))
            self._inorder(node.right, result)
