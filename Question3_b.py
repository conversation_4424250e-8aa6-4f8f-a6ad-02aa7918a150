class BSTNode:
    def __init__(self, key, value):
        self.key = key
        self.value = value
        self.left = None
        self.right = None

class BST:
    def __init__(self):
        self.root = None
    
    def insert(self, key, value):
        """Insert key-value pair into BST"""
        self.root = self._insert(self.root, key, value)
    
    def _insert(self, node, key, value):
        """Helper insert method"""
        if node is None:
            return BSTNode(key, value)
        
        # String comparison (lexicographical)
        if key < node.key:
            node.left = self._insert(node.left, key, value)
        elif key > node.key:
            node.right = self._insert(node.right, key, value)
        # If key exists, update value
        else:
            node.value = value
        
        return node
    
    def search(self, key):
        """Search for key in BST"""
        return self._search(self.root, key)
    
    def _search(self, node, key):
        """Helper search method"""
        if node is None:
            return None
        
        if key == node.key:
            return node.value
        elif key < node.key:
            return self._search(node.left, key)
        else:
            return self._search(node.right, key)
    
    def inorder(self):
        """Return list of (key, value) pairs in sorted order"""
        result = []
        self._inorder(self.root, result)
        return result
    
    def _inorder(self, node, result):
        """Helper inorder traversal"""
        if node:
            self._inorder(node.left, result)
            result.append((node.key, node.value))
            self._inorder(node.right, result)
