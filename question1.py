class Node:
    def __init__(self, data, left=None, right=None):
        self.data = data
        self.left = left
        self.right = right

def build_expr_tree_from_postfix(tokens: list[str]) -> Node:
    stack = []
    operators = {"+", "-", "/", "*"}
    for token in tokens:
        if token not in operators:  # Operand
            node = Node(token)
            stack.append(node)
        else:  # Operator
            right = stack.pop()
            left = stack.pop()
            node = Node(token, left, right)
            stack.append(node)

    return stack[-1]  # root



# ----------------------------
postfix_expression = "5 6 2 - *"
tokens = postfix_expression.split()   # ✅ split on spaces
root = build_expr_tree_from_postfix(tokens)




def evaluate(root: Node) -> float:
    if root.left is None and root.right is None:
        return float(root.data)
    
    left_val = evaluate(root.left)
    right_val = evaluate(root.right)

    if root.data == "+":
        return left_val + right_val
    elif root.data == "-":
        return left_val - right_val
    elif root.data == "*":
        return left_val * right_val
    elif root.data == "/":
        return left_val / right_val
    
    raise ValueError(f"Unknown operator: {root.data}")

value = evaluate(root)
print("\n", value)

def to_infix(root: Node):
    if root:
        if root.left or root.right:
            print("(", end="")
        to_infix(root.left)
        print(root.data, end="")
        to_infix(root.right)
        if root.left or root.right:
            print(")", end="")

print("Infix: ", end="")
to_infix(root)       