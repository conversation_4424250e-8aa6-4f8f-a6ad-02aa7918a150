#!/usr/bin/env python3
"""
Test script for Question 3 parts (a) and (b)
Tests double hash table and BST implementations
"""

# Import the implementations
import sys
import os

# Add current directory to path to import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test data
students = [
    ("S001", "Ada"),
    ("S010", "Kofi"), 
    ("S005", "Lerato"),
    ("S012", "Amara"),
    ("S003", "Naledi")
]

def test_question3a():
    """Test Question 3(a) - Double Hash Table"""
    print("=" * 60)
    print("TESTING QUESTION 3(a) - DOUBLE HASH TABLE")
    print("=" * 60)
    
    # Import and create double hash table
    from Question3_a import DoubleHashTable
    
    dht = DoubleHashTable()
    
    print(f"Table size (m): {dht.m}")
    print(f"Secondary prime (p): {dht.p}")
    print()
    
    # Test insertions with detailed output
    print("INSERTIONS:")
    print("-" * 40)
    
    for key, value in students:
        print(f"\nInserting ('{key}', '{value}'):")
        
        # Calculate hash values
        s_val = dht.S(key)
        h1_val = dht.h1(key)
        h2_val = dht.h2(key)
        
        print(f"  S('{key}') = {s_val}")
        print(f"  h1 = {s_val} % {dht.m} = {h1_val}")
        print(f"  h2 = {dht.p} - ({s_val} % {dht.p}) = {h2_val}")
        
        # Insert and show probe steps
        probe_steps = dht.put(key, value)
        print(f"  Probe steps: {probe_steps}")
        print(f"  Final index: {probe_steps[-1]}")
    
        """Display the current state of hash table"""
        print("\nHash Table State:")
        print("Index | Content")
        print("-" * 30)
        for i in range(dht.m):
            content = "Empty"
            if dht.hash_table[i] == "DELETED":
                content = "DELETED"
            elif dht.hash_table[i] is not None:
                content = f"ID: {dht.hash_table[i].student_id}"
            print(f"{i:5} | {content}")
    
    # Test retrieval
    print("\nTEST RETRIEVAL:")
    print("-" * 40)
    for key, expected_value in students:
        retrieved_value = dht.get(key)
        status = "✓" if retrieved_value == expected_value else "✗"
        print(f"{status} get('{key}') = '{retrieved_value}' (expected: '{expected_value}')")
    
    # Test deletion
    print("\nTEST DELETION:")
    print("-" * 40)
    test_key = "S005"
    print(f"Deleting '{test_key}'...")
    deleted = dht.delete(test_key)
    print(f"Deletion successful: {deleted}")
    print(f"Keys after deletion: {dht.keys}")
    
    # Try to retrieve deleted key
    retrieved = dht.get(test_key)
    print(f"Trying to get deleted key '{test_key}': {retrieved}")
    
    return dht

def test_question3b():
    """Test Question 3(b) - Binary Search Tree"""
    print("\n" + "=" * 60)
    print("TESTING QUESTION 3(b) - BINARY SEARCH TREE")
    print("=" * 60)

    # Import and create BST
    from Question3_b import BST

    bst = BST()

    # Test insertions
    print("INSERTIONS:")
    print("-" * 40)
    for key, value in students:
        print(f"Inserting ('{key}', '{value}')")
        try:
            bst.insert(key, value)
            print(f"  ✓ Successfully inserted")
        except ValueError as e:
            print(f"  ✗ Error: {e}")

    # Test duplicate key insertion (should fail)
    print("\nTEST DUPLICATE KEY INSERTION:")
    print("-" * 40)
    try:
        print("Attempting to insert duplicate key 'S001'...")
        bst.insert("S001", "Duplicate Ada")
        print("  ✗ ERROR: Duplicate insertion should have failed!")
    except ValueError as e:
        print(f"  ✓ Correctly rejected duplicate: {e}")

    # Test inorder traversal (should be sorted)
    print("\nINORDER TRAVERSAL (sorted by key):")
    print("-" * 40)
    sorted_list = bst.inorder()
    for i, (key, value) in enumerate(sorted_list, 1):
        print(f"{i}. ('{key}', '{value}')")

    # Verify sorting
    keys_only = [key for key, _ in sorted_list]
    is_sorted = keys_only == sorted(keys_only)
    print(f"\nKeys are properly sorted: {'✓' if is_sorted else '✗'}")

    # Test search functionality
    print("\nTEST SEARCH:")
    print("-" * 40)
    for key, expected_value in students:
        retrieved_value = bst.search(key)
        status = "✓" if retrieved_value == expected_value else "✗"
        print(f"{status} search('{key}') = '{retrieved_value}' (expected: '{expected_value}')")

    # Test search for non-existent key
    non_existent = "S999"
    result = bst.search(non_existent)
    print(f"search('{non_existent}') = {result} (should be None)")

    return bst

def compare_structures():
    """Compare the two data structures"""
    print("\n" + "=" * 60)
    print("COMPARISON OF DATA STRUCTURES")
    print("=" * 60)
    
    print("Double Hash Table:")
    print("- Average case O(1) insertion, search, deletion")
    print("- Uses open addressing with double hashing")
    print("- Memory efficient (arrays)")
    print("- No inherent ordering")
    
    print("\nBinary Search Tree:")
    print("- Average case O(log n) insertion, search, deletion")
    print("- Maintains sorted order")
    print("- Supports range queries and ordered traversal")
    print("- Uses more memory (node pointers)")
    
    print("\nFor the given student data:")
    print("- Hash table provides faster access")
    print("- BST provides sorted iteration")

def main():
    """Run all tests"""
    try:
        # Test Question 3(a)
        dht = test_question3a()
        
        # Test Question 3(b)
        bst = test_question3b()
        
        # Compare structures
        compare_structures()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
